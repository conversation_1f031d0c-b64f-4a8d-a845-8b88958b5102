<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Drum</title>
    <link rel="stylesheet" href="style.css" />
    <script src="./script.js"></script>
  </head>
  <body class="min-h-screen overflow-hidden">
    <!-- Add a header with instructions -->
    <header
      class=" overflow-hidden text-center p-4 text-white bg-black/40"
    >
      <h1 class="text-2xl md:text-4xl font-bold mb-2">JavaScript Drum Kit</h1>
      <p class="text-sm md:text-base">Press keys A-L to play sounds</p>
    </header>

    <!-- for centering the buttons -->
    <div
      class="flex items-center justify-center flex-wrap gap-3 md:gap-5 min-h-screen bg-[url(./background.jpg)] bg-cover bg-center p-4"
    >
      <div
        class="px-8 pt-5 btn-key btn-a text-4xl font-medium font-mono rounded-lg bg-black/40 text-white border-black border-4 btn-key"
      >
        <div class="flex flex-col">
          <kbd class="text-center">A</kbd
          ><span class="text-sm text-yellow-500 pb-2">CLAP</span>
        </div>
      </div>
      <div
        class="px-8 pt-5 btn-key btn-s text-4xl font-medium font-mono rounded-lg bg-black/40 text-white border-black border-4 btn-key"
      >
        <div class="flex flex-col">
          <kbd class="text-center">S</kbd
          ><span class="text-sm text-yellow-500 pb-2">HIHAT</span>
        </div>
      </div>
      <div
        class="px-8 pt-5 btn-key btn-d text-4xl font-medium font-mono rounded-lg bg-black/40 text-white border-black border-4 btn-key"
      >
        <div class="flex flex-col">
          <kbd class="text-center">D</kbd
          ><span class="text-sm text-yellow-500 pb-2">KICK</span>
        </div>
      </div>
      <div
        class="px-8 pt-5 btn-key btn-f text-4xl font-medium font-mono rounded-lg bg-black/40 text-white border-black border-4 btn-key"
      >
        <div class="flex flex-col">
          <kbd class="text-center">F</kbd
          ><span class="text-sm text-yellow-500 pb-2">OPENHAT</span>
        </div>
      </div>
      <div
        class="px-8 pt-5 btn-key btn-g text-4xl font-medium font-mono rounded-lg bg-black/40 text-white border-black border-4 btn-key"
      >
        <div class="flex flex-col">
          <kbd class="text-center">G</kbd
          ><span class="text-sm text-yellow-500 pb-2">BOOM</span>
        </div>
      </div>
      <div
        class="px-8 pt-5 btn-key btn-h text-4xl font-medium font-mono rounded-lg bg-black/40 text-white border-black border-4 btn-key"
      >
        <div class="flex flex-col">
          <kbd class="text-center">H</kbd
          ><span class="text-sm text-yellow-500 pb-2">RIDE</span>
        </div>
      </div>
      <div
        class="px-8 pt-5 btn-key btn-j text-4xl font-medium font-mono rounded-lg bg-black/40 text-white border-black border-4 btn-key"
      >
        <div class="flex flex-col">
          <kbd class="text-center">J</kbd
          ><span class="text-sm text-yellow-500 pb-2">SNARE</span>
        </div>
      </div>
      <div
        class="px-8 pt-5 btn-key btn-k text-4xl font-medium font-mono rounded-lg bg-black/40 text-white border-black border-4 btn-key"
      >
        <div class="flex flex-col">
          <kbd class="text-center">K</kbd
          ><span class="text-sm text-yellow-500 pb-2">TOM</span>
        </div>
      </div>
      <div
        class="px-8 pt-5 btn-key btn-l text-4xl font-medium font-mono rounded-lg bg-black/40 text-white border-black border-4 btn-key"
      >
        <div class="flex flex-col">
          <kbd class="text-center">L</kbd
          ><span class="text-sm text-yellow-500 pb-2">TINK</span>
        </div>
      </div>
    </div>
    <script>
      const typingKey = document.querySelectorAll(".btn-Key");
      // --------------------------------------------
      // for the keydown event
      document.addEventListener("keydown", (event) => {
        const pressedKey = event.key.toLowerCase();
        const validKeys = ["a", "s", "d", "f", "g", "h", "j", "k", "l"];

        if (validKeys.includes(pressedKey)) {
          audioPlayer(pressedKey);
          const element = document.querySelector(`.btn-${pressedKey}`);
          element.classList.add(
            // "bg-red-500",
            // Remove "text-4xl" to prevent layout shifts

            "scale-110",
            "border-yellow-500/90",
            "transition",
            "duration-[1ms]",
            "shadow-md",
            "shadow-yellow-500/50"
          );
        }
      });
      function audioPlayer(pressedKey) {
        const keySound = {
          a: "clap.wav",
          s: "hihat.wav",
          d: "kick.wav",
          f: "openhat.wav",
          g: "boom.wav",
          h: "ride.wav",
          j: "snare.wav",
          k: "tom.wav",
          l: "tink.wav",
        };

        const soundFile = keySound[pressedKey];
        if (soundFile) {
          const audio = new Audio(`./sounds/${soundFile}`);
          audio.currentTime = 0;
          audio.play();
        }
      }
      // -------------------------------------------------
      // for the keyup event
      document.addEventListener("keyup", (event) => {
        const pressedKey = event.key.toLowerCase();
        const validKeys = ["a", "s", "d", "f", "g", "h", "j", "k", "l"];

        if (validKeys.includes(pressedKey)) {
          const element = document.querySelector(`.btn-${pressedKey}`);
          element.classList.remove(
            // "bg-red-500",
            // Remove "text-4xl" here too

            "scale-110",
            "border-yellow-500/90",
            "duration-[1ms]",
            "shadow-sm",
            "shadow-yellow-500/50"
          );
        }
      });
    </script>
  </body>
</html>
