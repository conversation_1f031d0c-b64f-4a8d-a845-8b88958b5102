<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title><PERSON><PERSON><PERSON> 💪</title>
  <link rel="icon" href="https://fav.farm/✅" />
</head>
<body>
  <p><em>Psst: have a look at the JavaScript Console</em> 💁</p>
  <script>
    // Get your shorts on - this is an array workout!
    // ## Array Cardio Day 1

    // Some data we can work with

    const inventors = [
      { first: '<PERSON>', last: '<PERSON>', year: 1879, passed: 1955 },
      { first: '<PERSON>', last: '<PERSON>', year: 1643, passed: 1727 },
      { first: '<PERSON>', last: '<PERSON><PERSON><PERSON><PERSON>', year: 1564, passed: 1642 },
      { first: '<PERSON>', last: '<PERSON><PERSON><PERSON>', year: 1867, passed: 1934 },
      { first: '<PERSON>', last: '<PERSON><PERSON>', year: 1571, passed: 1630 },
      { first: '<PERSON><PERSON>', last: 'Copernicus', year: 1473, passed: 1543 },
      { first: '<PERSON>', last: '<PERSON><PERSON>', year: 1858, passed: 1947 },
      { first: '<PERSON>', last: '<PERSON><PERSON><PERSON><PERSON>', year: 1898, passed: 1979 },
      { first: '<PERSON>', last: 'Lovelace', year: 1815, passed: 1852 },
      { first: '<PERSON>', last: '<PERSON>e', year: 1855, passed: 1905 },
      { first: '<PERSON>se', last: '<PERSON>t<PERSON>', year: 1878, passed: 1968 },
      { first: '<PERSON>', last: '<PERSON><PERSON>st<PERSON>', year: 1829, passed: 1909 }
    ];

    const people = [
      '<PERSON>, <PERSON>', '<PERSON>ea, <PERSON>', '<PERSON>, <PERSON>', '<PERSON>ts<PERSON>, <PERSON>', '<PERSON>, <PERSON>', '<PERSON>, <PERSON>', '<PERSON>, R<PERSON>', '<PERSON>do<PERSON>, <PERSON>', '<PERSON>, <PERSON>',
      'Belloc, Hilaire', 'Begin, Menachem', 'Bellow, Saul', 'Benchley, Robert', 'Blair, Robert', 'Benenson, Peter', 'Benjamin, Walter', 'Berlin, Irving',
      'Benn, Tony', 'Benson, Leana', 'Bent, Silas', 'Berle, Milton', 'Berry, Halle', 'Biko, Steve', 'Beck, Glenn', 'Bergman, Ingmar', 'Black, Elk', 'Berio, Luciano',
      'Berne, Eric', 'Berra, Yogi', 'Berry, Wendell', 'Bevan, Aneurin', 'Ben-Gurion, David', 'Bevel, Ken', 'Biden, Joseph', 'Bennington, Chester', 'Bierce, Ambrose',
      'Billings, Josh', 'Birrell, Augustine', 'Blair, Tony', 'Beecher, Henry', 'Biondo, Frank'
    ];

    // Array.prototype.filter()
    // 1. Filter the list of inventors for those who were born in the 1500's
    const fifteen = inventors.filter(inventor => (inventor.year >= 1500 && inventor.year < 1600));

    console.table(fifteen);

    // Array.prototype.map()
    // 2. Give us an array of the inventor first and last names
    const fullNames = inventors.map(inventor => `${inventor.first} ${inventor.last}`);
    console.log(fullNames);

    // Array.prototype.sort()
    // 3. Sort the inventors by birthdate, oldest to youngest
    // const ordered = inventors.sort(function(a, b) {
    //   if(a.year > b.year) {
    //     return 1;
    //   } else {
    //     return -1;
    //   }
    // });

    const ordered = inventors.sort((a, b) => a.year > b.year ? 1 : -1);
    console.table(ordered);

    // Array.prototype.reduce()
    // 4. How many years did all the inventors live?
    const totalYears = inventors.reduce((total, inventor) => {
      return total + (inventor.passed - inventor.year);
    }, 0);

    console.log(totalYears);

    // 5. Sort the inventors by years lived
    const oldest = inventors.sort(function(a, b) {
      const lastInventor = a.passed - a.year;
      const nextInventor = b.passed - b.year;
      return lastInventor > nextInventor ? -1 : 1;
    });
    console.table(oldest);

    // 6. create a list of Boulevards in Paris that contain 'de' anywhere in the name
    // https://en.wikipedia.org/wiki/Category:Boulevards_in_Paris

    // const category = document.querySelector('.mw-category');
    // const links = Array.from(category.querySelectorAll('a'));
    // const de = links
    //             .map(link => link.textContent)
    //             .filter(streetName => streetName.includes('de'));

    // 7. sort Exercise
    // Sort the people alphabetically by last name
    const alpha = people.sort((lastOne, nextOne) => {
      const [aLast, aFirst] = lastOne.split(', ');
      const [bLast, bFirst] = nextOne.split(', ');
      return aLast > bLast ? 1 : -1;
    });
    console.log(alpha);

    // 8. Reduce Exercise
    // Sum up the instances of each of these
    const data = ['car', 'car', 'truck', 'truck', 'bike', 'walk', 'car', 'van', 'bike', 'walk', 'car', 'van', 'car', 'truck', 'pogostick'];

    const transportation = data.reduce(function(obj, item) {
      if (!obj[item]) {
        obj[item] = 0;
      }
      obj[item]++;
      return obj;
    }, {});

    console.log(transportation);

  </script>
</body>
</html>
