html {
  box-sizing: border-box;
}

*, *:before, *:after {
  box-sizing: inherit;
}

html {
  font-size: 10px;
  background: #ffc600;
}

.photobooth {
  background: white;
  max-width: 150rem;
  margin: 2rem auto;
  border-radius: 2px;
}

/*clearfix*/
.photobooth:after {
  content: '';
  display: block;
  clear: both;
}

.photo {
  width: 100%;
  float: left;
}

.player {
  position: absolute;
  top: 20px;
  right: 20px;
  width:200px;
}

/*
  Strip!
*/

.strip {
  padding: 2rem;
}

.strip img {
  width: 100px;
  overflow-x: scroll;
  padding: 0.8rem 0.8rem 2.5rem 0.8rem;
  box-shadow: 0 0 3px rgba(0,0,0,0.2);
  background: white;
}

.strip a:nth-child(5n+1) img { transform: rotate(10deg); }
.strip a:nth-child(5n+2) img { transform: rotate(-2deg); }
.strip a:nth-child(5n+3) img { transform: rotate(8deg); }
.strip a:nth-child(5n+4) img { transform: rotate(-11deg); }
.strip a:nth-child(5n+5) img { transform: rotate(12deg); }
